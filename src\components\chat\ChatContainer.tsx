import { useRef, useEffect, useState, useMemo } from "react";
import { Loader2 } from "lucide-react";

import { Message } from "@/types/chat";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MessageBubble } from "./MessageBubble";
import { MessageInput } from "./MessageInput";
import { ChatAvatar } from "./ChatAvatar";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "../auth/hooks/useAuth";

interface ChatContainerProps {
  messages: Message[];
  activeChat: any;
  currentUser: any;
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}

export const ChatContainer = ({
  messages,
  activeChat,
  currentUser,
  onSendMessage,
  isLoading,
}: ChatContainerProps) => {
  const { userProfile } = useAuth();

  const [isSent, setIsSent] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (messagesEndRef.current && scrollAreaRef.current) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "end",
        });
      }, 100);
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const showSampleMessage = useMemo(() => {
    if (isSent || !activeChat || isLoading) return false;

    const isJobOwner = activeChat.jobs?.user_id === currentUser?.id;
    const userMessageCount = messages.filter(
      (message) => message.sender_id === currentUser?.id
    ).length;

    return isJobOwner ? userMessageCount === 0 : userMessageCount <= 1;
  }, [messages, isSent, currentUser?.id, activeChat?.jobs?.user_id, isLoading]);

  if (!activeChat) {
    return (
      <div className="h-[600px] md:h-[600px] flex items-center justify-center text-center text-muted-foreground">
        <div>
          <h3 className="font-medium mb-2">Welkom bij je berichten</h3>
          <p>Selecteer een chat om berichten te bekijken</p>
        </div>
      </div>
    );
  }

  // Determine the recipient ID - if the current user is the job owner, send to the vakman, otherwise send to the job owner
  const recipientId =
    activeChat.jobs?.user_id === currentUser?.id
      ? activeChat.profiles?.id
      : activeChat.jobs?.user_id;

  const chatTitle = activeChat.jobs?.title || "Chat";
  const otherPartyName =
    activeChat.profiles?.company_name ||
    activeChat.profiles?.full_name ||
    `${activeChat.profiles?.first_name || ""} ${
      activeChat.profiles?.last_name || ""
    }`;

  const craftmanSampleMessages = [
    "Zal ik langskomen voor offerte?",
    "Zouden wij een afspraak kunnen inplannen?",
    "Wanneer komt het jou u uit om de klus te bespreken?",
  ];

  const ownerSampleMessages = [
    `Wanneer kun je langskomen kijken?`,
    "Heb je vandaag nog tijd?",
    "Stuur je prijsindicatie graag.",
  ];

  const handleSendSampleMessage = async (sample: string) => {
    setIsSent(true);
    const { error } = await supabase.from("messages").insert({
      content: sample,
      job_id: activeChat.jobs?.id,
      sender_id: userProfile?.id,
      receiver_id: recipientId,
      chat_id: activeChat?.slug,
    });

    if (!error) {
      // Create in-app notification for the receiver
      try {
        const senderName =
          userProfile?.company_name ||
          `${userProfile?.first_name} ${userProfile?.last_name}`.trim() ||
          "Een gebruiker";

        await supabase.rpc("create_notification", {
          p_user_id: recipientId,
          p_title: "Nieuw bericht",
          p_message: `Je hebt een nieuw bericht ontvangen van ${senderName}`,
          p_type: "info",
          p_action_url: "/gesprekken",
          p_metadata: {
            sender_id: userProfile?.id,
            job_id: activeChat.jobs?.id,
            chat_id: activeChat?.slug,
          },
        });
      } catch (error) {
        console.error("Error creating message notification:", error);
      }
    }
  };

  return (
    <div className="sm:h-[calc(100dvh-200px)] h-[calc(100dvh-150px)] flex flex-col">
      <div className="px-4 md:px-6 py-4 bg-background/80 backdrop-blur-sm border-b">
        <div className="flex items-center gap-4">
          <ChatAvatar
            profilePhotoUrl={activeChat.profiles?.profile_photo_url}
            size="lg"
          />
          <div>
            <h3 className="font-medium text-lg">{chatTitle}</h3>
            <p className="text-sm text-muted-foreground">
              Chat met: {otherPartyName}
            </p>
          </div>
        </div>
      </div>
      <ScrollArea
        ref={scrollAreaRef}
        className="flex-1 px-0 md:px-4 relative pb-12"
        style={{ height: "calc(100% - 140px)" }}
      >
        <div className="space-y-4 min-h-full py-4">
          {isLoading ? (
            <div className="h-full flex items-center justify-center">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <p>Nog geen berichten in deze chat</p>
              <p className="text-sm">
                Stuur een bericht om het gesprek te starten
              </p>
            </div>
          ) : (
            messages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                isCurrentUser={message.sender_id === currentUser?.id}
              />
            ))
          )}
          <div className="w-full flex justify-center">
            {showSampleMessage && (
              <div className="flex sm:flex-row flex-col gap-4 absolute bottom-20 w-full sm:w-auto">
                {(activeChat.jobs?.user_id === currentUser?.id
                  ? ownerSampleMessages
                  : craftmanSampleMessages
                ).map((item) => (
                  <div
                    className="bg-muted rounded-2xl p-4 cursor-pointer text-[13px] sm:text-base"
                    onClick={() => handleSendSampleMessage(item)}
                  >
                    {item}
                  </div>
                ))}
              </div>
            )}
          </div>
          <div ref={messagesEndRef} className="h-1" />
        </div>
      </ScrollArea>
      <div className="p-4 bg-background/80 backdrop-blur-sm border-t fixed bottom-0 sm:w-[72%]">
        <MessageInput
          onSend={onSendMessage}
          disabled={isLoading}
          jobId={activeChat.jobs?.id}
          receiverId={recipientId}
          chatId={activeChat?.slug}
        />
      </div>
    </div>
  );
};
