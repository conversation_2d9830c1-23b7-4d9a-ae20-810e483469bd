// src/BlogDetails.js
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { client, urlFor } from '@/lib/sanity';
import BlockContent from '@sanity/block-content-to-react';
import RecentPostCard from './RecentPostCard'; // Make sure the path is correct

export function BlogDetails() {
  const { slug } = useParams();
  const [blog, setBlog] = useState(null);
  const [recentPosts, setRecentPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!slug) {
      setLoading(false);
      return;
    }

    const fetchBlogData = async () => {
      try {
        const blogQuery = `*[_type == "post" && slug.current == $slug][0]{
          _id,
          title,
          mainImage,
          description,
          _createdAt,
          "categories": categories[]->{ title }
        }`;
        const blogData = await client.fetch(blogQuery, { slug });

        if (!blogData) {
          throw new Error('Blog post not found.');
        }
        setBlog(blogData);

        const recentPostsQuery = `*[_type == "post" && _id != $currentId] | order(_createdAt desc) [0...5] {
          _id,
          title,
          mainImage,
          slug
        }`;
        const recentData = await client.fetch(recentPostsQuery, { currentId: blogData._id });
        setRecentPosts(recentData);

      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogData();
  }, [slug]);

  // === THE COMPLETE SERIALIZERS OBJECT RESTORED ===
  // This tells BlockContent how to render each element with your custom styles.
  const serializers = {
    types: {
      // Handles all text blocks (paragraphs, headings, etc.)
      block: (props) => {
        const { style = 'normal' } = props.node;

        switch (style) {
          case 'h1':
            return <h1 className="text-4xl font-extrabold my-6 text-gray-900">{props.children}</h1>;
          case 'h2':
            return <h2 className="text-3xl font-bold my-5 border-b pb-2">{props.children}</h2>;
          case 'h3':
            return <h3 className="text-2xl font-semibold my-4 text-gray-800">{props.children}</h3>;
          case 'h4':
            return <h4 className="text-xl font-medium my-3 text-gray-700">{props.children}</h4>;
          case 'blockquote':
            return <blockquote className="border-l-4 border-gray-300 italic text-gray-600 pl-4 my-6">{props.children}</blockquote>;
          default:
            return <p className="text-lg my-4 leading-relaxed">{props.children}</p>;
        }
      },
      // Handles images inserted into the body content
      image: ({ node }) => {
        if (!node || !node.asset) return null;
        return (
          <figure className="my-8">
            <img
              src={urlFor(node).width(800).auto('format').url()}
              alt={node.alt}
              className="w-full h-auto rounded-lg shadow-lg"
              loading="lazy"
            />
            {node.caption && (
              <figcaption className="text-center text-sm text-gray-600 mt-2 italic">
                {node.caption}
              </figcaption>
            )}
          </figure>
        );
      },
    },
    // Handles inline elements like links
    marks: {
      link: ({ mark, children }) => (
        <a href={mark.href} className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">
          {children}
        </a>
      ),
    },
  };

  if (loading) return <div className="text-center text-white py-10">Loading blog...</div>;
  if (error) return <div className="text-center text-red-500 py-10">Error fetching blog: {error.message}</div>;
  if (!blog) return <div className="text-center text-gray-400 py-10">Blog not found.</div>;

  const categoryTitle = blog.categories && blog.categories.length > 0 ? blog.categories[0].title : 'General';

  return (
    <div className="max-w-7xl mx-auto p-4 md:p-8 flex flex-col lg:flex-row gap-12">
      <article className="w-full lg:w-2/3">
        <div className="mb-8">
          <span className="w-fit bg-lightBlue text-white text-sm font-medium px-4 py-1 rounded-full">
            {categoryTitle}
          </span>
          <h1 className="text-xl md:text-3xl font-bold mt-2">{blog.title}</h1>
          <div className="flex items-center text-gray-500">
            <span className='block mt-6'>{new Date(blog._createdAt).toLocaleDateString()}</span>
          </div>
        </div>

        {blog.mainImage && (
           <img
            src={urlFor(blog.mainImage).width(800).url()}
            alt={blog.title}
            className="w-full h-auto rounded-lg shadow-md mb-8"
          />
        )}
       
        <div className="max-w-none">
          {blog.description && (
            <BlockContent
              blocks={blog.description}
              projectId={client.config().projectId}
              dataset={client.config().dataset}
              serializers={serializers}
            />
          )}
        </div>
      </article>

      <aside className="w-full lg:w-1/3 lg:sticky top-8 self-start">
        <h2 className="text-2xl font-bold mb-6 text-gray-900">andere verhalen</h2>
        <div>
          {recentPosts.map((post) => (
            <RecentPostCard key={post._id} post={post} />
          ))}
        </div>
      </aside>
    </div>
  );
}