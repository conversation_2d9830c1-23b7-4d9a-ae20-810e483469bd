// src/components/BlogPostCard.js
import { urlFor } from '@/lib/sanity';
import React from 'react';

const BlogPostCard = ({ post }) => {
  // A post might have multiple categories, we'll display the first one.
  const firstCategory = post.categories && post.categories.length > 0 ? post.categories[0] : null;


  console.log("post ",post.description)
  return (
    // This is a link that wraps the entire card for better UX
    <a href={`/weblogs/${post.slug.current}`} className="block group ">
      <div className="bg-gradient-to-b from-white to-gray-50 rounded-3xl shadow-2xl p-3 max-w-2xl mx-auto border border-gray-100">
        <div className="relative mx-auto h-52">
          {/* We must check if mainImage exists before rendering */}
          {post.mainImage ? (
            <img
              className="rounded-2xl w-full h-full object-cover"
              src={urlFor(post.mainImage).height(300).width(500).url()}
              alt={post.title}
            />
          ) : (
            <div className="rounded-2xl w-full h-full bg-gray-700 flex items-center justify-center">
              <span className="text-gray-500">No Image</span>
            </div>
          )}
        </div>


        {/* Display the first category if it exists */}
        {firstCategory && (
          <span className="my-2 mt-5 block w-fit bg-primary text-white text-xs font-medium px-4 py-1 rounded-2xl">
            {firstCategory.title}
          </span>
        )}
        
        <div>
          <h3 className=" font-medium text-base mt-6 mb-3">
            {post.title}
          </h3>
          {/* Use the line-clamp utility for the description */} 
    <p className="text-sm text-gray-600 mb-6 line-clamp-2">
    {post.plainDescription}
  </p>
          <span className="inline-block mt-3 text-xs font-bold text-primary">
            Read more →
          </span>
        </div>
      </div>
    </a>
  );
};

export default BlogPostCard;