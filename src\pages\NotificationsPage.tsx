import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Trash2,
  Filter,
  Search,
  X,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useNotifications, type Notification } from "@/hooks/useNotifications";
import {
  formatTimeAgo,
  getNotificationIcon,
  getNotificationBadgeVariant,
} from "@/lib/notifications";

const NotificationsPage = () => {
  const [filteredNotifications, setFilteredNotifications] = useState<
    Notification[]
  >([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>(
    []
  );
  const navigate = useNavigate();

  const {
    notifications,
    loading,
    unreadCount,
    markAsRead,
    markAsUnread,
    markMultipleAsRead,
    markAllAsRead,
    deleteNotification,
    deleteMultipleNotifications,
  } = useNotifications();

  useEffect(() => {
    filterNotifications();
  }, [notifications, searchTerm, activeTab]);

  const filterNotifications = () => {
    let filtered = notifications;

    // Filter by tab
    if (activeTab === "unread") {
      filtered = filtered.filter((n) => !n.read);
    } else if (activeTab === "read") {
      filtered = filtered.filter((n) => n.read);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (n) =>
          n.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          n.message.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredNotifications(filtered);
  };

  const handleMarkAsRead = async (notificationIds: string[]) => {
    if (notificationIds.length === 1) {
      await markAsRead(notificationIds[0]);
    } else {
      await markMultipleAsRead(notificationIds);
    }
  };

  const handleMarkAsUnread = async (notificationIds: string[]) => {
    for (const id of notificationIds) {
      await markAsUnread(id);
    }
  };

  const handleDeleteNotifications = async (notificationIds: string[]) => {
    if (notificationIds.length === 1) {
      await deleteNotification(notificationIds[0]);
    } else {
      await deleteMultipleNotifications(notificationIds);
    }
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const toggleSelection = (notificationId: string) => {
    setSelectedNotifications((prev) =>
      prev.includes(notificationId)
        ? prev.filter((id) => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  const selectAll = () => {
    setSelectedNotifications(filteredNotifications.map((n) => n.id));
  };

  const clearSelection = () => {
    setSelectedNotifications([]);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Bell className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold text-foreground tracking-wide">
              Notificaties
            </h1>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-sm">
                {unreadCount} nieuw
              </Badge>
            )}
          </div>
          <p className="text-muted-foreground text-lg leading-relaxed">
            Blijf op de hoogte van belangrijke updates en berichten
          </p>
        </div>

        {/* Search and Actions */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Zoek in notificaties..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSearchTerm("")}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className="flex gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="flex items-center gap-2"
                  >
                    <CheckCheck className="h-4 w-4" />
                    Alles gelezen
                  </Button>
                )}

                {selectedNotifications.length > 0 && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Acties ({selectedNotifications.length})
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => handleMarkAsRead(selectedNotifications)}
                      >
                        <Check className="h-4 w-4 mr-2" />
                        Markeer als gelezen
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          handleMarkAsUnread(selectedNotifications)
                        }
                      >
                        <Bell className="h-4 w-4 mr-2" />
                        Markeer als ongelezen
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          handleDeleteNotifications(selectedNotifications)
                        }
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Verwijderen
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs and Notifications */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-6">
            <TabsTrigger value="all" className="text-base">
              Alle ({notifications.length})
            </TabsTrigger>
            <TabsTrigger value="unread" className="text-base">
              Ongelezen ({unreadCount})
            </TabsTrigger>
            <TabsTrigger value="read" className="text-base">
              Gelezen ({notifications.length - unreadCount})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {/* Selection Controls */}
            {filteredNotifications.length > 0 && (
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={
                    selectedNotifications.length ===
                    filteredNotifications.length
                      ? clearSelection
                      : selectAll
                  }
                  className="h-8 px-2"
                >
                  {selectedNotifications.length === filteredNotifications.length
                    ? "Deselecteer alles"
                    : "Selecteer alles"}
                </Button>
                {selectedNotifications.length > 0 && (
                  <span>{selectedNotifications.length} geselecteerd</span>
                )}
              </div>
            )}

            {/* Notifications List */}
            {filteredNotifications.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    {searchTerm
                      ? "Geen resultaten gevonden"
                      : "Geen notificaties"}
                  </h3>
                  <p className="text-muted-foreground">
                    {searchTerm
                      ? "Probeer een andere zoekterm"
                      : "Je hebt momenteel geen notificaties"}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {filteredNotifications.map((notification) => (
                  <Card
                    key={notification.id}
                    className={`transition-all duration-200 hover:shadow-md cursor-pointer ${
                      !notification.read
                        ? "border-l-4 border-l-primary bg-primary/5"
                        : ""
                    } ${
                      selectedNotifications.includes(notification.id)
                        ? "ring-2 ring-primary"
                        : ""
                    }`}
                    onClick={() => toggleSelection(notification.id)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        {/* Selection Checkbox */}
                        <div className="flex items-center pt-1">
                          <div
                            className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                              selectedNotifications.includes(notification.id)
                                ? "bg-primary border-primary"
                                : "border-muted-foreground"
                            }`}
                          >
                            {selectedNotifications.includes(
                              notification.id
                            ) && <Check className="h-3 w-3 text-white" />}
                          </div>
                        </div>

                        {/* Notification Icon */}
                        <div className="text-2xl pt-1">
                          {getNotificationIcon(notification.type)}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-4 mb-2">
                            <h3
                              className={`text-lg font-semibold leading-tight ${
                                !notification.read
                                  ? "text-foreground"
                                  : "text-muted-foreground"
                              }`}
                            >
                              {notification.title}
                            </h3>
                            <div className="flex items-center gap-2 flex-shrink-0">
                              <Badge
                                variant={getNotificationBadgeVariant(
                                  notification.type
                                )}
                                className="text-xs"
                              >
                                {notification.type}
                              </Badge>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-primary rounded-full"></div>
                              )}
                            </div>
                          </div>

                          <p
                            className={`text-base leading-relaxed mb-3 ${
                              !notification.read
                                ? "text-foreground"
                                : "text-muted-foreground"
                            }`}
                          >
                            {notification.message}
                          </p>

                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">
                              {formatTimeAgo(notification.created_at)}
                            </span>

                            {notification.action_url && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (notification.action_url) {
                                    navigate(notification.action_url);
                                  }
                                }}
                                className="text-xs"
                              >
                                Bekijken
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Load More Button (for future pagination) */}
        {filteredNotifications.length > 0 && (
          <div className="text-center mt-8">
            <Button variant="outline" className="px-8">
              Meer laden
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationsPage;
