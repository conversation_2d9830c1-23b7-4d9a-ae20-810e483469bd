/**
 * @description This component renders a beautiful email/password login page for the Klusgebied platform.
 * It features a classic authentication process with email and password input fields with stunning animations.
 * The component uses teal accents consistent with the brand, world-class form design, and proper error handling.
 * Key variables include email, password, isLogin state for toggling between login/register, and loading states for seamless user experience.
 */
import { useState, useEffect } from "react";
import {
  ArrowLeft,
  Mail,
  Lock,
  CheckCircle,
  Loader,
  Eye,
  EyeOff,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import usePageTitle from "../../hooks/usePageTitle";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { AuthError } from "@supabase/supabase-js";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { profileSchema } from "@/schemas/profileSchema";
import { ProfileFormData } from "@/types/auth";

const LoginPage = () => {
  usePageTitle("Inloggen | Klusgebied");
  const [isLogin, setIsLogin] = useState(true); // true: login, false: register
  // Step state for registration
  const [registerStep, setRegisterStep] = useState<
    "email" | "password" | "profile" | "done"
  >("email");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const navigate = useNavigate();
  const [registerError, setRegisterError] = useState("");
  const [registerPassword, setRegisterPassword] = useState("");
  const [registerShowPassword, setRegisterShowPassword] = useState(false);

  const {
    register: registerEmail,
    handleSubmit: handleEmailSubmit,
    formState: { errors: emailErrors },
    setError: setEmailFormError,
  } = useForm<{ email: string }>();

  const {
    register: registerPasswordField,
    handleSubmit: handlePasswordSubmit,
    formState: { errors: passwordErrors },
    setError: setPasswordFormError,
  } = useForm<{ password: string; confirmPassword: string }>();

  const {
    register: registerProfileField,
    handleSubmit: handleProfileSubmit,
    formState: { errors: profileErrors },
    setValue: setProfileValue,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: email,
      city: "",
      street_address: "",
      house_number: "",
      house_number_addition: "",
      phone_number: "",
      company_name: "",
      postal_code: "",
      user_type: "vakman",
    },
    mode: "onChange",
  });

  // Keep email in sync in the form if it changes
  useEffect(() => {
    setProfileValue("email", email);
  }, [email, setProfileValue]);

  const getErrorMessage = (error: AuthError) => {
    if (error.message.includes("Invalid login credentials")) {
      return "Onjuiste inloggegevens. Controleer je email en wachtwoord.";
    }
    if (error.message.includes("Email not confirmed")) {
      return "Je email is nog niet bevestigd. Check je inbox voor de bevestigingslink.";
    }
    return "Er is een fout opgetreden bij het inloggen. Probeer het opnieuw.";
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    if (!email || !password) {
      setError("Vul alle velden in");
      return;
    }

    setIsLoading(true);
    setError("");
    setSuccess("");

    const { data: authData, error } = await supabase.auth.signInWithPassword({
      email,
      password: password,
    });

    if (error) {
      console.error("Login error:", error);
      setError(getErrorMessage(error));
      setIsLoading(false);
      return;
    }

    if (authData?.user) {
      toast({
        title: "Succesvol ingelogd",
        description: "Welkom terug!",
      });

      // Get returnUrl from search params
      const params = new URLSearchParams(location.search);
      const returnUrl = params.get("returnUrl");
      navigate(returnUrl || "/dashboard");
    }

    setIsLoading(false);

    // const redirectUrl = `https://klusgebied.nl/direct-inloggen?email=${encodeURIComponent(
    // 	email
    // )}&passord=${encodeURIComponent(password)}`;

    // // Redirect to the external URL
    // window.location.href = redirectUrl;
  };

  const handleRegister = async (e) => {
    e.preventDefault();
    if (!email || !password) {
      setError("Vul alle velden in");
      return;
    }

    if (password.length < 6) {
      setError("Wachtwoord moet minimaal 6 karakters lang zijn");
      return;
    }

    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      // Validate existing user
      if (await checkExistingUser(email)) {
        throw new Error(
          "Dit emailadres heeft al een account. Log in met je bestaande account."
        );
      }

      // Sign up user
      const { data: authData, error: signUpError } = await supabase.auth.signUp(
        {
          email,
          password,
          options: {
            emailRedirectTo: window.location.origin,
          },
        }
      );

      if (signUpError || !authData.user) {
        throw (
          signUpError ||
          new Error("Er ging iets mis bij het aanmaken van je account.")
        );
      }

      toast({
        title: "Registratie succesvol",
        description:
          "Controleer uw e-mailinbox om uw e-mailadres te verifiëren.",
      });

      navigate("/dashboard");

      // Update profile
      const profileData = {
        user_type: "vakman",
        email,
      };

      const { error: profileError } = await supabase
        .from("profiles")
        .update(profileData)
        .eq("id", authData.user.id);

      if (profileError) throw profileError;

      // Send notification email
      await sendNotificationEmail();
    } catch (error: unknown) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "Registratie mislukt",
        description:
          error instanceof Error
            ? error.message
            : "Er is iets misgegaan bij het registreren. Probeer het opnieuw.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkExistingUser = async (email: string) => {
    try {
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("id, user_type")
        .eq("email", email)
        .maybeSingle();

      if (profileError) {
        console.error("Error checking profile:", profileError);
        throw profileError;
      }

      return !!profile;
    } catch (error) {
      console.error("Error in checkExistingUser:", error);
      return true;
    }
  };

  const sendNotificationEmail = async () => {
    const { error: emailError } = await supabase.functions.invoke(
      "send-email",
      {
        body: {
          to: ["<EMAIL>"],
          subject: "Nieuwe gebruiker geregistreerd",
          html: `
			<h2>Nieuwe gebruiker geregistreerd op Klusgebied</h2>
			<p><strong>Email:</strong> ${email}</p>
			<p><strong>Type gebruiker:</strong> Vakman</p>
			<p><a href="https://admin.klusgebied.nl/beheerder/gebruikers" style="color: #0066cc;">Bekijk in admin panel</a></p>
		  `,
        },
      }
    );

    if (emailError) {
      console.error("Error sending notification email:", emailError);
    }
  };

  const onRegisterEmail = async (data: { email: string }) => {
    setRegisterError("");
    setIsLoading(true);
    try {
      const exists = await checkExistingUser(data.email);
      if (exists) {
        setRegisterError(
          "Email adres al geregistreerd, log in of verander wachtwoord"
        );
        setEmailFormError("email", {
          type: "manual",
          message: "Email bestaat al",
        });
        setIsLoading(false);
        return;
      }
      setEmail(data.email);
      setRegisterStep("password");
      setIsLoading(false);
    } catch (err) {
      setRegisterError("Er is een fout opgetreden. Probeer het opnieuw.");
      setIsLoading(false);
    }
  };

  // Password validation helper
  const validatePassword = (password: string) => {
    const minLength = password.length >= 6;
    const hasLowerCase = /[a-z]/.test(password);
    const hasUpperCase = /[A-Z]/.test(password);
    const hasDigit = /\d/.test(password);
    const hasSymbol = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    return minLength && hasLowerCase && hasUpperCase && hasDigit && hasSymbol;
  };

  // Registration: Handle password step
  const onRegisterPassword = (data: {
    password: string;
    confirmPassword: string;
  }) => {
    setRegisterError("");
    if (!validatePassword(data.password)) {
      setPasswordFormError("password", {
        type: "manual",
        message:
          "Wachtwoord moet minimaal 6 tekens bevatten, hoofdletter, kleine letter, cijfer en speciaal teken.",
      });
      return;
    }
    if (data.password !== data.confirmPassword) {
      setPasswordFormError("confirmPassword", {
        type: "manual",
        message: "Wachtwoorden komen niet overeen.",
      });
      return;
    }
    setRegisterPassword(data.password);
    setRegisterStep("profile");
  };

  // Registration: Handle profile step
  const onRegisterProfile = async (data: ProfileFormData) => {
    setRegisterError("");
    setIsLoading(true);
    try {
      // 1. Register user with Supabase
      const { data: authData, error: signUpError } = await supabase.auth.signUp(
        {
          email,
          password: registerPassword,
          options: {
            data: {
              user_type: data.user_type,
              first_name: data.first_name,
              last_name: data.last_name,
            },
            emailRedirectTo: window.location.origin,
          },
        }
      );
      if (signUpError || !authData.user) {
        throw (
          signUpError ||
          new Error("Er ging iets mis bij het aanmaken van je account.")
        );
      }

      // 2. Update profile in Supabase
      const profileData = {
        ...data,
        full_name: `${data.first_name} ${data.last_name}`,
        email,
      };
      const { error: profileError } = await supabase
        .from("profiles")
        .update(profileData)
        .eq("id", authData.user.id);
      if (profileError) throw profileError;

      navigate("/dashboard");

      // 3. Send admin notification email
      const { error: emailError } = await supabase.functions.invoke(
        "send-email",
        {
          body: {
            to: ["<EMAIL>"],
            subject: "Nieuwe gebruiker geregistreerd",
            html: `
            <h2>Nieuwe gebruiker geregistreerd op Klusgebied</h2>
            <p><strong>Naam:</strong> ${data.first_name} ${data.last_name}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Type gebruiker:</strong> ${data.user_type}</p>
            <p><strong>Telefoon:</strong> ${data.phone_number || "-"}</p>
            <p><strong>Adres:</strong> ${data.street_address} ${
              data.house_number
            }${
              data.house_number_addition ? "-" + data.house_number_addition : ""
            }, ${data.postal_code} ${data.city}</p>
            <p><strong>Bedrijfsnaam:</strong> ${data.company_name || "-"}</p>
            <p><a href="${
              window.location.origin
            }/beheerder/gebruikers" style="color: #0066cc;">Bekijk in admin panel</a></p>
          `,
          },
        }
      );
      if (emailError) {
        console.error("Error sending notification email:", emailError);
      }

      toast({
        title: "Registratie succesvol",
        description:
          "Controleer uw e-mailinbox om uw e-mailadres te verifiëren.",
      });
      // setRegisterStep("done");
    } catch (error: any) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "Registratie mislukt",
        description:
          error instanceof Error
            ? error.message
            : "Er is iets misgegaan bij het registreren. Probeer het opnieuw.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50/30 flex items-center justify-center px-4 pt-28 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-teal-100 rounded-full opacity-30 motion-preset-float"></div>
      <div className="absolute bottom-20 right-10 w-24 h-24 bg-blue-100 rounded-full opacity-30 motion-preset-float motion-delay-500"></div>

      <div className="w-full max-w-2xl relative">
        {/* Back Button */}
        <button
          onClick={() => navigate("/")}
          className="flex items-center space-x-2 text-slate-600 hover:text-teal-600 mb-8 transition-all duration-300 hover:scale-105"
        >
          <ArrowLeft className="w-5 h-5" />
          <span className="font-medium">Terug naar home</span>
        </button>

        {/* Login/Register Card */}
        <div className="bg-white rounded-3xl shadow-2xl p-8 lg:p-10 border border-slate-100 motion-preset-slide-up">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <img
                src="https://heyboss.heeyo.ai/chat-images/ChatGPT Image 25 jun 2025, 15_48_09_32j6tyj5.png"
                alt="Klusgebied Logo"
                className="w-16 h-16"
              />
            </div>

            <h1 className="text-3xl font-bold text-slate-800 mb-3">
              {isLogin ? "Welkom terug" : "Account aanmaken"}
            </h1>
            <p className="text-slate-600">
              {isLogin
                ? "Log in met je e-mailadres en wachtwoord"
                : "Maak een nieuw account aan om verder te gaan"}
            </p>
          </div>

          {/* Login Form */}
          {isLogin ? (
            <form onSubmit={handleLogin} className="space-y-6">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-semibold text-slate-700 mb-3"
                >
                  E-mailadres
                </label>
                <div className="relative">
                  <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="bijv. <EMAIL>"
                    className="w-full pl-12 pr-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-semibold text-slate-700 mb-3"
                >
                  Wachtwoord
                </label>
                <div className="relative">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder={
                      isLogin ? "Je wachtwoord" : "Minimaal 6 karakters"
                    }
                    className="w-full pl-12 pr-12 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors duration-200"
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>

              {error && (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 px-4 py-3 rounded-xl border border-red-200">
                  <span className="text-sm font-medium">{error}</span>
                </div>
              )}

              {success && (
                <div className="flex items-center space-x-2 text-green-600 bg-green-50 px-4 py-3 rounded-xl border border-green-200">
                  <CheckCircle className="w-5 h-5" />
                  <span className="text-sm font-medium">{success}</span>
                </div>
              )}

              <button
                type="submit"
                disabled={isLoading || !email || !password}
                className="w-full bg-teal-500 text-white py-4 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3 hover:-translate-y-0.5"
              >
                {isLoading ? (
                  <>
                    <Loader className="w-5 h-5 animate-spin" />
                    <span>
                      {isLogin ? "Inloggen..." : "Account aanmaken..."}
                    </span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-5 h-5" />
                    <span>{isLogin ? "Inloggen" : "Account aanmaken"}</span>
                  </>
                )}
              </button>
            </form>
          ) : (
            // Registration Multi-Step
            <div>
              {registerStep === "email" && (
                <form
                  onSubmit={handleEmailSubmit(onRegisterEmail)}
                  className="space-y-6"
                >
                  <div>
                    <label
                      htmlFor="register-email"
                      className="block text-sm font-semibold text-slate-700 mb-3"
                    >
                      E-mailadres
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                      <input
                        id="register-email"
                        type="email"
                        {...registerEmail("email", {
                          required: "E-mailadres is verplicht",
                          pattern: {
                            value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                            message: "Voer een geldig e-mailadres in",
                          },
                        })}
                        placeholder="bijv. <EMAIL>"
                        className="w-full pl-12 pr-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                    </div>
                    {emailErrors.email && (
                      <span className="text-red-600 text-sm mt-2 block">
                        {emailErrors.email.message}
                      </span>
                    )}
                  </div>
                  {registerError && (
                    <div className="flex items-center space-x-2 text-red-600 bg-red-50 px-4 py-3 rounded-xl border border-red-200">
                      <span className="text-sm font-medium">
                        {registerError}
                      </span>
                    </div>
                  )}
                  <button
                    type="submit"
                    className="w-full bg-teal-500 text-white py-3 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3 hover:-translate-y-0.5"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader className="w-5 h-5 animate-spin" />
                    ) : (
                      <CheckCircle className="w-5 h-5" />
                    )}
                    <span>Volgende</span>
                  </button>
                </form>
              )}
              {registerStep === "password" && (
                <form
                  onSubmit={handlePasswordSubmit(onRegisterPassword)}
                  className="space-y-6"
                >
                  <div>
                    <label
                      htmlFor="register-password"
                      className="block text-sm font-semibold text-slate-700 mb-3"
                    >
                      Wachtwoord
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                      <input
                        id="register-password"
                        type={registerShowPassword ? "text" : "password"}
                        {...registerPasswordField("password", {
                          required: "Wachtwoord is verplicht",
                        })}
                        placeholder="Minimaal 6 tekens, hoofdletter, cijfer, symbool"
                        className="w-full pl-12 pr-12 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                      <button
                        type="button"
                        onClick={() =>
                          setRegisterShowPassword(!registerShowPassword)
                        }
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors duration-200"
                      >
                        {registerShowPassword ? (
                          <EyeOff className="w-5 h-5" />
                        ) : (
                          <Eye className="w-5 h-5" />
                        )}
                      </button>
                    </div>
                    {passwordErrors.password && (
                      <span className="text-red-600 text-sm mt-2 block">
                        {passwordErrors.password.message}
                      </span>
                    )}
                  </div>
                  <div>
                    <label
                      htmlFor="register-confirm-password"
                      className="block text-sm font-semibold text-slate-700 mb-3"
                    >
                      Bevestig wachtwoord
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                      <input
                        id="register-confirm-password"
                        type={registerShowPassword ? "text" : "password"}
                        {...registerPasswordField("confirmPassword", {
                          required: "Bevestig je wachtwoord",
                        })}
                        placeholder="Herhaal wachtwoord"
                        className="w-full pl-12 pr-12 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                    </div>
                    {passwordErrors.confirmPassword && (
                      <span className="text-red-600 text-sm mt-2 block">
                        {passwordErrors.confirmPassword.message}
                      </span>
                    )}
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-teal-500 text-white py-3 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3 hover:-translate-y-0.5"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader className="w-5 h-5 animate-spin" />
                    ) : (
                      <CheckCircle className="w-5 h-5" />
                    )}
                    <span>Volgende</span>
                  </button>
                </form>
              )}
              {registerStep === "profile" && (
                <form
                  onSubmit={handleProfileSubmit(onRegisterProfile)}
                  className="space-y-6"
                >
                  {/* Email field (read-only) */}
                  <div>
                    <label
                      htmlFor="register-email-profile"
                      className="block text-sm font-semibold text-slate-700 mb-3"
                    >
                      E-mailadres
                    </label>
                    <input
                      id="register-email-profile"
                      type="email"
                      {...registerProfileField("email")}
                      value={email}
                      readOnly
                      className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl bg-gray-100 focus:outline-none text-lg cursor-not-allowed"
                      tabIndex={-1}
                    />
                    {profileErrors.email && (
                      <span className="text-red-600 text-sm mt-2 block">
                        {profileErrors.email.message}
                      </span>
                    )}
                  </div>
                  {/* First name & Last name */}
                  <div className="flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0">
                    <div className="flex-1">
                      <label
                        htmlFor="register-first-name"
                        className="block text-sm font-semibold text-slate-700 mb-3"
                      >
                        Voornaam
                      </label>
                      <input
                        id="register-first-name"
                        type="text"
                        {...registerProfileField("first_name")}
                        placeholder="Bijv. Jan"
                        className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                      {profileErrors.first_name && (
                        <span className="text-red-600 text-sm mt-2 block">
                          {profileErrors.first_name.message}
                        </span>
                      )}
                    </div>
                    <div className="flex-1">
                      <label
                        htmlFor="register-last-name"
                        className="block text-sm font-semibold text-slate-700 mb-3"
                      >
                        Achternaam
                      </label>
                      <input
                        id="register-last-name"
                        type="text"
                        {...registerProfileField("last_name")}
                        placeholder="Bijv. Jansen"
                        className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                      {profileErrors.last_name && (
                        <span className="text-red-600 text-sm mt-2 block">
                          {profileErrors.last_name.message}
                        </span>
                      )}
                    </div>
                  </div>
                  {/* Phone number & Company name */}
                  <div className="flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0">
                    <div className="flex-1">
                      <label
                        htmlFor="register-phone-number"
                        className="block text-sm font-semibold text-slate-700 mb-3"
                      >
                        Telefoonnummer
                      </label>
                      <input
                        id="register-phone-number"
                        type="text"
                        {...registerProfileField("phone_number")}
                        placeholder="Bijv. 0612345678"
                        className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                      {profileErrors.phone_number && (
                        <span className="text-red-600 text-sm mt-2 block">
                          {profileErrors.phone_number.message}
                        </span>
                      )}
                    </div>
                    <div className="flex-1">
                      <label
                        htmlFor="register-company-name"
                        className="block text-sm font-semibold text-slate-700 mb-3"
                      >
                        Bedrijfsnaam
                      </label>
                      <input
                        id="register-company-name"
                        type="text"
                        {...registerProfileField("company_name")}
                        placeholder="Bedrijfsnaam (optioneel)"
                        className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                      {profileErrors.company_name && (
                        <span className="text-red-600 text-sm mt-2 block">
                          {profileErrors.company_name.message}
                        </span>
                      )}
                    </div>
                  </div>
                  {/* Street address & House number */}
                  <div className="flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0">
                    <div className="flex-1">
                      <label
                        htmlFor="register-street-address"
                        className="block text-sm font-semibold text-slate-700 mb-3"
                      >
                        Straat
                      </label>
                      <input
                        id="register-street-address"
                        type="text"
                        {...registerProfileField("street_address")}
                        placeholder="Straatnaam"
                        className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                      {profileErrors.street_address && (
                        <span className="text-red-600 text-sm mt-2 block">
                          {profileErrors.street_address.message}
                        </span>
                      )}
                    </div>
                    <div className="flex-1">
                      <label
                        htmlFor="register-house-number"
                        className="block text-sm font-semibold text-slate-700 mb-3"
                      >
                        Huisnummer
                      </label>
                      <input
                        id="register-house-number"
                        type="text"
                        {...registerProfileField("house_number")}
                        placeholder="Nr."
                        className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                      {profileErrors.house_number && (
                        <span className="text-red-600 text-sm mt-2 block">
                          {profileErrors.house_number.message}
                        </span>
                      )}
                    </div>
                  </div>
                  {/* House number addition & Postal code */}
                  <div className="flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0">
                    <div className="flex-1">
                      <label
                        htmlFor="register-house-number-addition"
                        className="block text-sm font-semibold text-slate-700 mb-3"
                      >
                        Toevoeging
                      </label>
                      <input
                        id="register-house-number-addition"
                        type="text"
                        {...registerProfileField("house_number_addition")}
                        placeholder="A/B/C (optioneel)"
                        className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                      {profileErrors.house_number_addition && (
                        <span className="text-red-600 text-sm mt-2 block">
                          {profileErrors.house_number_addition.message}
                        </span>
                      )}
                    </div>
                    <div className="flex-1">
                      <label
                        htmlFor="register-postal-code"
                        className="block text-sm font-semibold text-slate-700 mb-3"
                      >
                        Postcode
                      </label>
                      <input
                        id="register-postal-code"
                        type="text"
                        {...registerProfileField("postal_code")}
                        placeholder="1234AB"
                        className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                        disabled={isLoading}
                      />
                      {profileErrors.postal_code && (
                        <span className="text-red-600 text-sm mt-2 block">
                          {profileErrors.postal_code.message}
                        </span>
                      )}
                    </div>
                  </div>
                  {/* City (alone if odd number) */}
                  <div>
                    <label
                      htmlFor="register-city"
                      className="block text-sm font-semibold text-slate-700 mb-3"
                    >
                      Plaats
                    </label>
                    <input
                      id="register-city"
                      type="text"
                      {...registerProfileField("city")}
                      placeholder="Bijv. Amsterdam"
                      className="w-full px-4 py-4 border-2 border-slate-200 rounded-xl focus:border-teal-500 focus:outline-none transition-all duration-300 text-lg"
                      disabled={isLoading}
                    />
                    {profileErrors.city && (
                      <span className="text-red-600 text-sm mt-2 block">
                        {profileErrors.city.message}
                      </span>
                    )}
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-teal-500 text-white py-3 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3 hover:-translate-y-0.5"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader className="w-5 h-5 animate-spin" />
                    ) : (
                      <CheckCircle className="w-5 h-5" />
                    )}
                    <span>Afronden</span>
                  </button>
                </form>
              )}
              {registerStep === "done" && (
                <div className="space-y-4 text-center">
                  <p>
                    Registratie succesvol! Controleer uw e-mail om uw account te
                    activeren.
                  </p>
                  <button
                    className="w-full bg-teal-500 text-white py-3 rounded-xl"
                    onClick={() => setRegisterStep("email")}
                  >
                    Opnieuw registreren
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Toggle between Login/Register */}
          <div className="mt-6 text-center">
            <button
              onClick={() => {
                setIsLogin(!isLogin);
                setError("");
                setSuccess("");
                setPassword("");
                setRegisterStep("email"); // Reset registration step
              }}
              className="text-teal-600 hover:text-teal-700 font-medium transition-colors duration-300"
            >
              {isLogin
                ? "Nog geen account? Maak er een aan"
                : "Al een account? Log in"}
            </button>
          </div>

          {/* Trust Elements */}
          <div className="mt-8 pt-6 border-t border-slate-200">
            <div className="flex items-center justify-center space-x-6 text-sm text-slate-500">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>100% Veilig</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                <span>Versleutelde verbinding</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
