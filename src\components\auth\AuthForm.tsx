import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { AuthError } from "@supabase/supabase-js";

import { EmailStep } from "./EmailStep";
import { PasswordStep } from "./PasswordStep";
import { RegistrationForm } from "./RegistrationForm";
import { ProfileFormData, ViewType, UserType } from "@/types/auth";
import { AuthFormSubmitButton } from "./AuthFormSubmitButton";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useTranslation } from "react-i18next";

interface AuthFormProps {
  view: ViewType;
  setView: (view: ViewType) => void;
  onEmailSubmit: () => void;
  forcedUserType?: "vakman" | "klusaanvrager";
}

export const AuthForm = ({
  view,
  setView,
  onEmailSubmit,
  forcedUserType,
}: AuthFormProps) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [step, setStep] = useState<"email" | "verification" | "registration">(
    "email"
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
    const { t } = useTranslation(); // <-- 2. Initialize translator

  const userType = forcedUserType || "vakman";

  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const {
    register,
    formState: { errors },
    handleSubmit,
    reset,
  } = useForm();

  const checkExistingUser = async (email: string) => {
    try {
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("id, user_type")
        .eq("email", email)
        .maybeSingle();

      if (profileError) {
        console.error("Error checking profile:", profileError);
        throw profileError;
      }

      return !!profile;
    } catch (error) {
      console.error("Error in checkExistingUser:", error);
      return true;
    }
  };

  const getErrorMessage = (error: AuthError) => {
    if (error.message.includes("Invalid login credentials")) {
      return "Onjuiste inloggegevens. Controleer je email en wachtwoord.";
    }
    if (error.message.includes("Email not confirmed")) {
      return "Je email is nog niet bevestigd. Check je inbox voor de bevestigingslink.";
    }
    return "Er is een fout opgetreden bij het inloggen. Probeer het opnieuw.";
  };

  const handleEmailStep = async (data: { email: string }) => {
    try {
      setIsLoading(true);
      setError(null);

      if (view === "sign_up") {
        const userExists = await checkExistingUser(data.email);

        if (userExists) {
          toast({
            variant: "destructive",
            title: "Email bestaat al",
            description:
              "Email adres al geregistreerd, log in of verander wachtwoord",
          });
          setError(
            "Email adres al geregistreerd, log in of verander wachtwoord"
          );
          return;
        }
      }

      setEmail(data.email);
      setStep("verification");
      onEmailSubmit();
    } catch (error) {
      console.error("Error in email step:", error);
      setError(
        "Er is een onverwachte fout opgetreden. Probeer het later opnieuw."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const checkIfValidPassword = (password: string) => {
    const minLength = password.length >= 6;
    const hasLowerCase = /[a-z]/.test(password);
    const hasUpperCase = /[A-Z]/.test(password);
    const hasDigit = /\d/.test(password);
    const hasSymbol = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

    const isValid =
      minLength && hasLowerCase && hasUpperCase && hasDigit && hasSymbol;

    return {
      isValid,
      errors: {
        minLength: !minLength
          ? "Wachtwoord moet minimaal 6 tekens bevatten"
          : null,
        hasLowerCase: !hasLowerCase
          ? "Wachtwoord moet kleine letters bevatten"
          : null,
        hasUpperCase: !hasUpperCase
          ? "Wachtwoord moet hoofdletters bevatten"
          : null,
        hasDigit: !hasDigit ? "Wachtwoord moet cijfers bevatten" : null,
        hasSymbol: !hasSymbol
          ? "Wachtwoord moet speciale tekens bevatten"
          : null,
      },
    };
  };

  const handlePasswordStep = async (data: {
    password: string;
    confirmPassword?: string;
  }) => {
    try {
      setIsLoading(true);
      setError(null);

      if (view === "sign_up") {
        const { isValid, errors: passwordErrors } = checkIfValidPassword(
          data.password
        );

        if (!isValid) {
          // Get the first error message from the passwordErrors object
          const firstError = Object.values(passwordErrors).find(
            (error) => error !== null
          );
          setError(firstError || "Wachtwoord voldoet niet aan de vereisten");
          return;
        }

        if (data.password !== data.confirmPassword) {
          setError(
            "De wachtwoorden komen niet overeen. Controleer of je twee keer hetzelfde wachtwoord hebt ingevuld."
          );
          return;
        }
      }

      setPassword(data.password);

      if (view === "sign_in") {
        const { data: authData, error } =
          await supabase.auth.signInWithPassword({
            email,
            password: data.password,
          });

        if (error) {
          console.error("Login error:", error);
          setError(getErrorMessage(error));
          return;
        }

        if (authData?.user) {
          toast({
            title: "Succesvol ingelogd",
            description: "Welkom terug!",
          });

          // Get returnUrl from search params
          const params = new URLSearchParams(location.search);
          const returnUrl = params.get("returnUrl");
          navigate(returnUrl || "/");
        }
      } else {
        setStep("registration");
      }
    } catch (error) {
      console.error("Error in password step:", error);
      setError(
        "Er is een onverwachte fout opgetreden. Probeer het later opnieuw."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegistrationComplete = async (formData: ProfileFormData) => {
    try {
      setIsLoading(true);

      // Validate existing user
      if (await checkExistingUser(email)) {
        throw new Error(
          "Dit emailadres heeft al een account. Log in met je bestaande account."
        );
      }

      // Prepare user metadata
      const userMetadata = {
        user_type: userType,
        first_name: formData.first_name,
        last_name: formData.last_name,
      };

      // Sign up user
      const { data: authData, error: signUpError } = await supabase.auth.signUp(
        {
          email,
          password,
          options: {
            data: userMetadata,
            emailRedirectTo: window.location.origin,
          },
        }
      );

      if (signUpError || !authData.user) {
        throw (
          signUpError ||
          new Error("Er ging iets mis bij het aanmaken van je account.")
        );
      }

      toast({
        title: "Registratie succesvol",
        description:
          "Controleer uw e-mailinbox om uw e-mailadres te verifiëren.",
      });

      // Update profile
      const profileData = {
        ...formData,
        user_type: userType,
        full_name: `${formData.first_name} ${formData.last_name}`,
        email,
      };

      const { error: profileError } = await supabase
        .from("profiles")
        .update(profileData)
        .eq("id", authData.user.id);

      if (profileError) throw profileError;

      // Send notification email
      await sendNotificationEmail(formData);
    } catch (error: unknown) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "Registratie mislukt",
        description:
          error instanceof Error
            ? error.message
            : "Er is iets misgegaan bij het registreren. Probeer het opnieuw.",
      });
      setStep("email");
      reset();
    } finally {
      setIsLoading(false);
    }
  };

  const sendNotificationEmail = async (formData: ProfileFormData) => {
    const { error: emailError } = await supabase.functions.invoke(
      "send-email",
      {
        body: {
          to: ["<EMAIL>"],
          subject: "Nieuwe gebruiker geregistreerd",
          html: `
          <h2>Nieuwe gebruiker geregistreerd op Klusgebied</h2>
          <p><strong>Naam:</strong> ${formData.first_name} ${formData.last_name}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Type gebruiker:</strong> ${userType}</p>
          <p><a href="${window.location.origin}/beheerder/gebruikers" style="color: #0066cc;">Bekijk in admin panel</a></p>
        `,
        },
      }
    );

    if (emailError) {
      console.error("Error sending notification email:", emailError);
    }
  };

  if (step === "registration") {
    return (
      <RegistrationForm
        userType={userType as UserType}
        onSubmit={handleRegistrationComplete}
        email={email}
      />
    );
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <form
        onSubmit={
          step === "email"
            ? handleSubmit(handleEmailStep)
            : handleSubmit(handlePasswordStep)
        }
      >
        {step === "email" && (
          <EmailStep
            register={register}
            errors={errors}
            view={view}
            onExistingAccount={() => setView("sign_in")}
            onNewAccount={() => setView("sign_up")}
            onSubmit={(email) => {
              setEmail(email);
              setStep("verification");
            }}
          />
        )}

        {step === "verification" && (
          <PasswordStep
            register={register}
            errors={errors}
            email={email}
            onBack={() => {
              setStep("email");
              setError(null);
            }}
            view={view}
          />
        )}

        <div className="mt-6">
          <AuthFormSubmitButton isLoading={isLoading} step={step} view={view} />
        </div>
      </form>

        <div className="text-center text-sm">
        {view === "sign_in" ? (
          <p className="text-gray-600">
            {t("authPromptNoAccount")}
            <button type="button" /* ... */>
              {t("authLinkRegisterHere")}
            </button>
          </p>
        ) : (
          <p className="text-gray-600">
            {t("authPromptHasAccount")}
           
               <button
              type="button"
              className="text-primary/70 hover:text-primary font-medium no-underline"
              onClick={() => {
                setView("sign_in");
                setError(null);
                setStep("email");
              }}
            >
              {t('authLinkLoginHere')}
            </button>
          </p>
        )}
      </div>
    </div>
  );
};
