-- Create notifications table for user notifications
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VA<PERSON><PERSON><PERSON>(20) NOT NULL DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
  read BOOLEAN NOT NULL DEFAULT FALSE,
  action_url VARCHAR(500),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_user_read ON notifications(user_id, read);
CREATE INDEX IF NOT EXISTS idx_notifications_user_created ON notifications(user_id, created_at DESC);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_notifications_updated_at
  BEFORE UPDATE ON notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_notifications_updated_at();

-- Enable RLS (Row Level Security)
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own notifications
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own notifications (mark as read/unread)
CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own notifications
CREATE POLICY "Users can delete their own notifications" ON notifications
  FOR DELETE USING (auth.uid() = user_id);

-- Only system/admin can insert notifications (via functions)
CREATE POLICY "System can insert notifications" ON notifications
  FOR INSERT WITH CHECK (true);

-- Create function to create notification
CREATE OR REPLACE FUNCTION create_notification(
  p_user_id UUID,
  p_title VARCHAR(255),
  p_message TEXT,
  p_type VARCHAR(20) DEFAULT 'info',
  p_action_url VARCHAR(500) DEFAULT NULL,
  p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO notifications (user_id, title, message, type, action_url, metadata)
  VALUES (p_user_id, p_title, p_message, p_type, p_action_url, p_metadata)
  RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(notification_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE notifications 
  SET read = TRUE, updated_at = NOW()
  WHERE id = notification_id AND user_id = auth.uid();
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to mark notification as unread
CREATE OR REPLACE FUNCTION mark_notification_unread(notification_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE notifications 
  SET read = FALSE, updated_at = NOW()
  WHERE id = notification_id AND user_id = auth.uid();
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to mark all notifications as read for a user
CREATE OR REPLACE FUNCTION mark_all_notifications_read(p_user_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  target_user_id UUID;
  updated_count INTEGER;
BEGIN
  -- Use provided user_id or current authenticated user
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  UPDATE notifications 
  SET read = TRUE, updated_at = NOW()
  WHERE user_id = target_user_id AND read = FALSE;
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to delete notification
CREATE OR REPLACE FUNCTION delete_notification(notification_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  DELETE FROM notifications 
  WHERE id = notification_id AND user_id = auth.uid();
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(p_user_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  target_user_id UUID;
  unread_count INTEGER;
BEGIN
  -- Use provided user_id or current authenticated user
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  SELECT COUNT(*) INTO unread_count
  FROM notifications 
  WHERE user_id = target_user_id AND read = FALSE;
  
  RETURN unread_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create job notification for craftsmen
CREATE OR REPLACE FUNCTION notify_craftsmen_new_job(
  p_job_id UUID,
  p_job_title VARCHAR(255),
  p_services TEXT[]
)
RETURNS INTEGER AS $$
DECLARE
  craftsman_record RECORD;
  notification_count INTEGER := 0;
BEGIN
  -- Find craftsmen with matching services
  FOR craftsman_record IN
    SELECT DISTINCT p.id, p.first_name
    FROM profiles p
    WHERE p.user_type = 'vakman' 
    AND p.services && p_services  -- Array overlap operator
    AND p.id != (SELECT user_id FROM jobs WHERE id = p_job_id)  -- Don't notify job creator
  LOOP
    -- Create notification for each matching craftsman
    PERFORM create_notification(
      craftsman_record.id,
      'Nieuwe klus beschikbaar',
      'Er is een nieuwe klus beschikbaar die past bij jouw diensten: ' || p_job_title,
      'info',
      '/banen/' || p_job_id::text
    );
    
    notification_count := notification_count + 1;
  END LOOP;
  
  RETURN notification_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert some sample notifications for testing (remove in production)
-- This will only work if there are existing users in the profiles table
DO $$
DECLARE
  sample_user_id UUID;
BEGIN
  -- Get a sample user ID (first vakman user)
  SELECT id INTO sample_user_id 
  FROM profiles 
  WHERE user_type = 'vakman' 
  LIMIT 1;
  
  -- Only insert if we found a user
  IF sample_user_id IS NOT NULL THEN
    -- Insert sample notifications
    PERFORM create_notification(
      sample_user_id,
      'Welkom bij Klusgebied!',
      'Bedankt voor je registratie. Vul je profiel aan om meer klussen te ontvangen.',
      'info',
      '/profiel'
    );
    
    PERFORM create_notification(
      sample_user_id,
      'Nieuwe klus beschikbaar',
      'Er is een nieuwe klus beschikbaar in jouw gebied: Badkamer renovatie in Amsterdam',
      'info',
      '/banen'
    );
  END IF;
END $$;

-- Enable realtime for notifications table
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

-- Comment for documentation
COMMENT ON TABLE notifications IS 'User notifications for jobs, system messages, and updates';
COMMENT ON COLUMN notifications.type IS 'Notification type: info, success, warning, error';
COMMENT ON COLUMN notifications.metadata IS 'Additional data for the notification in JSON format';
COMMENT ON COLUMN notifications.action_url IS 'Optional URL to navigate to when notification is clicked';
