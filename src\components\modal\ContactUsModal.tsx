import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { PostgrestError } from "@supabase/supabase-js";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
} from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";
import { generateSecurePassword } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";
import { sendNotificationEmail } from "@/lib/utils";
import { generateSampleJobDescription, getJobTitle } from "@/lib/jobUtils";
import { questionnaire } from "../werkwijze/new-jobs/questionnaire";

interface ContactUsModalProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  jobType: string;
}

const ContactUsModal = ({
  isOpen,
  setIsOpen,
  jobType,
}: ContactUsModalProps) => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    postalCode: "",
    houseNumber: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would handle the form submission
    console.log("Form submitted:", formData);

    const {
      email,
      message,
      firstName,
      phone,
      lastName,
      postalCode,
      houseNumber,
    } = formData;
    const password = generateSecurePassword();
    const jobDescription = generateSampleJobDescription(jobType, message);

    setIsSubmitting(true);
    try {
      // Step 1: User Authentication
      const { data: authData, error: signUpError } = await supabase.auth.signUp(
        {
          email,
          password,
          options: {
            data: { user_type: "klusaanvrager" },
            emailRedirectTo: `${location.origin}/kies-uw-vakman`,
          },
        }
      );

      if (signUpError) {
        if (signUpError.message.includes("exist")) {
          throw new Error(
            "Dit e-mailadres is al in gebruik. Probeer in te loggen."
          );
        }
        throw new Error("Er ging iets mis bij het aanmaken van uw account.");
      }

      if (!authData.user) {
        throw new Error("Er ging iets mis bij het aanmaken van uw account.");
      }

      const userId = authData.user.id;

      // Execute profile update and job creation in parallel
      const [profileResult, jobResult] = await Promise.all([
        // Update Profile
        supabase
          .from("profiles")
          .update({
            full_name: `${firstName} ${lastName}`,
            phone_number: phone,
            first_name: firstName,
            last_name: lastName,
            house_number: houseNumber,
            postal_code: postalCode,
          })
          .eq("id", userId),

        // Create Job
        supabase
          .from("jobs")
          .insert({
            title: getJobTitle(jobType),
            description: jobDescription,
            user_id: userId,
            services:
              questionnaire.find((q) => q.id === jobType)?.services || [],
          })
          .select()
          .single(),
      ]);

      if (profileResult.error) {
        throw new Error(
          "Er ging iets mis bij het opslaan van uw profielgegevens."
        );
      }

      if (jobResult.error) {
        const error = jobResult.error as PostgrestError;
        if (error.code === "23503") {
          throw new Error(
            "Er ging iets mis bij het koppelen van de klus aan uw profiel."
          );
        }
        throw new Error("Er ging iets mis bij het aanmaken van de klus.");
      }

      // Success handling
      navigate("/banen", { replace: true });
      toast({
        title: "Registratie succesvol",
        description:
          "Controleer uw e-mailinbox om uw e-mailadres te verifiëren.",
      });

      // Send user information notification
      await sendNotificationEmail({
        to: [email],
        subject: "Welkom bij Klusgebied - Uw accountgegevens",
        content: `
    <div style="font-family: sans-serif; color: #333;">
      <h2>Welkom bij Klusgebied</h2>
      <p>Beste ${firstName} ${lastName},</p>
      <p>Uw account is succesvol aangemaakt. Hieronder vindt u uw inloggegevens:</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Wachtwoord:</strong> ${password}</p>
      <p style="color: #666; font-size: 14px;">Wij raden u aan dit wachtwoord direct te wijzigen na uw eerste inlog.</p>
      <p><a href="${window.location.origin}/auth" style="color: #0066cc;">Klik hier om in te loggen</a></p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
      <p style="color: #666; font-size: 12px;">Dit is een automatisch gegenereerd bericht. Bewaar deze email op een veilige plaats.</p>
    </div>
  `,
      });

      // Send user registration notification
      await sendNotificationEmail({
        to: ["<EMAIL>"],
        subject: "Nieuwe gebruiker geregistreerd",
        content: `
          <div style="font-family: sans-serif; color: #333;">
            <h2>Nieuwe gebruiker geregistreerd op Klusgebied</h2>
            <p><strong>Naam:</strong> ${firstName} ${lastName}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Type gebruiker:</strong> klusaanvrager</p>
            <p><a href="${window.location.origin}/beheerder/gebruikers" style="color: #0066cc;">Bekijk in admin panel</a></p>
          </div>
        `,
      });

      // Send new job notification
      await sendNotificationEmail({
        to: ["<EMAIL>"],
        subject: "Nieuwe klusopdracht geplaatst",
        content: `
          <div style="font-family: sans-serif; color: #333;">
            <h2>Nieuwe klusopdracht</h2>
            <p><strong>Titel:</strong> ${getJobTitle(jobType)}</p>
            <p><strong>Klant:</strong> ${firstName} ${lastName}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><a href="${
              window.location.origin
            }/beheerder/berichten" style="color: #0066cc;">Bekijk in admin panel</a></p>
          </div>
        `,
      });
    } catch (error) {
      console.error("Job creation failed:", error);
      toast({
        variant: "destructive",
        title: "Er is iets misgegaan",
        description:
          error instanceof Error
            ? error.message
            : "De klus kon niet worden toegevoegd. Probeer het opnieuw.",
      });
    } finally {
      setIsSubmitting(false);
    }

    // Reset form
    setFormData({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      postalCode: "",
      houseNumber: "",
      message: "",
    });
    setIsOpen(false);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent>
        <DialogHeader>Contact opnemen</DialogHeader>
        <DialogDescription>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              {/* First row - Name fields */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    placeholder="Voornaam*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
                <div className="flex-1">
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    placeholder="Achternaam*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              {/* Second row - Email and Phone */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="E-mailadres*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
                <div className="flex-1">
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Telefoonnummer*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              {/* Third row - Postal code and House number */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    name="postalCode"
                    value={formData.postalCode}
                    onChange={handleChange}
                    placeholder="Postcode*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
                <div className="flex-1">
                  <input
                    type="text"
                    name="houseNumber"
                    value={formData.houseNumber}
                    onChange={handleChange}
                    placeholder="Huisnummer*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              {/* Message box */}
              <div>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="Je bericht..."
                  rows={4}
                  required
                  className="w-full p-3 border border-gray-300 rounded-md"
                />
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-[#40cfc1] text-white py-3 px-4 rounded-md hover:bg-[#35b5a8] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? "Bezig met verzenden..." : "Contact opnemen"}
              </button>

              <p className="text-xs text-gray-500 mt-4">
                Door je informatie in te dienen, ga je ermee akkoord deze naar
                Klusgebied te sturen, die deze zal verwerken en gebruiken
                volgens hun privacyverklaring.
              </p>
            </div>
          </form>
        </DialogDescription>
      </DialogContent>
    </Dialog>
  );
};

export default ContactUsModal;
