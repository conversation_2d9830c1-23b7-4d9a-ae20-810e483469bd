import { supabase } from "@/integrations/supabase/client";

export interface CreateNotificationParams {
  userId: string;
  title: string;
  message: string;
  type?: "info" | "success" | "warning" | "error";
  actionUrl?: string;
  metadata?: Record<string, any>;
}

export interface NotificationTemplate {
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  actionUrl?: string;
}

/**
 * Create a new notification for a user
 */
export const createNotification = async (params: CreateNotificationParams): Promise<string | null> => {
  try {
    const { data, error } = await supabase.rpc("create_notification", {
      p_user_id: params.userId,
      p_title: params.title,
      p_message: params.message,
      p_type: params.type || "info",
      p_action_url: params.actionUrl || null,
      p_metadata: params.metadata || {}
    });

    if (error) {
      console.error("Error creating notification:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error creating notification:", error);
    return null;
  }
};

/**
 * Create notifications for multiple users
 */
export const createBulkNotifications = async (
  userIds: string[],
  template: NotificationTemplate
): Promise<number> => {
  try {
    const promises = userIds.map(userId =>
      createNotification({
        userId,
        title: template.title,
        message: template.message,
        type: template.type,
        actionUrl: template.actionUrl
      })
    );

    const results = await Promise.all(promises);
    const successCount = results.filter(result => result !== null).length;
    
    return successCount;
  } catch (error) {
    console.error("Error creating bulk notifications:", error);
    return 0;
  }
};

/**
 * Notification templates for common scenarios
 */
export const NotificationTemplates = {
  // Job-related notifications
  newJobAvailable: (jobTitle: string, jobId: string): NotificationTemplate => ({
    title: "Nieuwe klus beschikbaar",
    message: `Er is een nieuwe klus beschikbaar die past bij jouw diensten: ${jobTitle}`,
    type: "info",
    actionUrl: `/banen/${jobId}`
  }),

  jobResponseReceived: (jobTitle: string): NotificationTemplate => ({
    title: "Nieuwe reactie ontvangen",
    message: `Je hebt een nieuwe reactie ontvangen op je klus '${jobTitle}'`,
    type: "success",
    actionUrl: "/gesprekken"
  }),

  jobCompleted: (jobTitle: string): NotificationTemplate => ({
    title: "Klus voltooid",
    message: `De klus '${jobTitle}' is succesvol voltooid`,
    type: "success",
    actionUrl: "/banen"
  }),

  jobCancelled: (jobTitle: string): NotificationTemplate => ({
    title: "Klus geannuleerd",
    message: `De klus '${jobTitle}' is geannuleerd`,
    type: "warning",
    actionUrl: "/banen"
  }),

  // Profile-related notifications
  profileIncomplete: (): NotificationTemplate => ({
    title: "Profiel incompleet",
    message: "Je profiel is nog niet compleet. Voeg meer informatie toe om meer klussen te ontvangen.",
    type: "warning",
    actionUrl: "/profiel"
  }),

  profileApproved: (): NotificationTemplate => ({
    title: "Profiel goedgekeurd",
    message: "Je profiel is goedgekeurd en je kunt nu klussen ontvangen!",
    type: "success",
    actionUrl: "/profiel"
  }),

  profileRejected: (reason?: string): NotificationTemplate => ({
    title: "Profiel afgekeurd",
    message: `Je profiel is afgekeurd. ${reason ? `Reden: ${reason}` : "Controleer je gegevens en probeer opnieuw."}`,
    type: "error",
    actionUrl: "/profiel"
  }),

  // Balance-related notifications
  balanceUpdated: (newBalance: number): NotificationTemplate => ({
    title: "Saldo bijgewerkt",
    message: `Je saldo is bijgewerkt. Nieuw saldo: €${newBalance.toFixed(2)}`,
    type: "success",
    actionUrl: "/evenwicht"
  }),

  lowBalance: (currentBalance: number): NotificationTemplate => ({
    title: "Laag saldo",
    message: `Je saldo is laag (€${currentBalance.toFixed(2)}). Voeg geld toe om te kunnen reageren op klussen.`,
    type: "warning",
    actionUrl: "/evenwicht"
  }),

  paymentReceived: (amount: number): NotificationTemplate => ({
    title: "Betaling ontvangen",
    message: `Je hebt een betaling van €${amount.toFixed(2)} ontvangen`,
    type: "success",
    actionUrl: "/evenwicht"
  }),

  // Message-related notifications
  newMessage: (senderName: string): NotificationTemplate => ({
    title: "Nieuw bericht",
    message: `Je hebt een nieuw bericht ontvangen van ${senderName}`,
    type: "info",
    actionUrl: "/gesprekken"
  }),

  // System notifications
  systemMaintenance: (startTime: string, endTime: string): NotificationTemplate => ({
    title: "Systeem onderhoud",
    message: `Er vindt gepland onderhoud plaats van ${startTime} tot ${endTime}`,
    type: "info"
  }),

  welcomeMessage: (): NotificationTemplate => ({
    title: "Welkom bij Klusgebied!",
    message: "Bedankt voor je registratie. Vul je profiel aan om meer klussen te ontvangen.",
    type: "info",
    actionUrl: "/profiel"
  }),

  // Review notifications
  newReview: (rating: number): NotificationTemplate => ({
    title: "Nieuwe beoordeling",
    message: `Je hebt een nieuwe beoordeling ontvangen: ${rating} sterren`,
    type: "success",
    actionUrl: "/beoordelingen"
  }),

  reviewReminder: (jobTitle: string): NotificationTemplate => ({
    title: "Beoordeling gevraagd",
    message: `Vergeet niet om een beoordeling achter te laten voor de klus '${jobTitle}'`,
    type: "info",
    actionUrl: "/beoordelingen"
  })
};

/**
 * Notify craftsmen about a new job
 */
export const notifyCraftsmenNewJob = async (
  jobId: string,
  jobTitle: string,
  services: string[]
): Promise<number> => {
  try {
    const { data, error } = await supabase.rpc("notify_craftsmen_new_job", {
      p_job_id: jobId,
      p_job_title: jobTitle,
      p_services: services
    });

    if (error) {
      console.error("Error notifying craftsmen:", error);
      return 0;
    }

    return data || 0;
  } catch (error) {
    console.error("Error notifying craftsmen:", error);
    return 0;
  }
};

/**
 * Send welcome notification to new user
 */
export const sendWelcomeNotification = async (userId: string): Promise<boolean> => {
  const notificationId = await createNotification({
    userId,
    ...NotificationTemplates.welcomeMessage()
  });

  return notificationId !== null;
};

/**
 * Send profile incomplete notification
 */
export const sendProfileIncompleteNotification = async (userId: string): Promise<boolean> => {
  const notificationId = await createNotification({
    userId,
    ...NotificationTemplates.profileIncomplete()
  });

  return notificationId !== null;
};

/**
 * Send balance update notification
 */
export const sendBalanceUpdateNotification = async (
  userId: string,
  newBalance: number
): Promise<boolean> => {
  const notificationId = await createNotification({
    userId,
    ...NotificationTemplates.balanceUpdated(newBalance)
  });

  return notificationId !== null;
};

/**
 * Send new message notification
 */
export const sendNewMessageNotification = async (
  userId: string,
  senderName: string
): Promise<boolean> => {
  const notificationId = await createNotification({
    userId,
    ...NotificationTemplates.newMessage(senderName)
  });

  return notificationId !== null;
};

/**
 * Format time ago for notifications
 */
export const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return "Zojuist";
  if (diffInMinutes < 60) return `${diffInMinutes} min geleden`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} uur geleden`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays} dag${diffInDays > 1 ? 'en' : ''} geleden`;
  
  return date.toLocaleDateString('nl-NL');
};

/**
 * Get notification icon based on type
 */
export const getNotificationIcon = (type: "info" | "success" | "warning" | "error"): string => {
  switch (type) {
    case "success":
      return "✅";
    case "warning":
      return "⚠️";
    case "error":
      return "❌";
    default:
      return "ℹ️";
  }
};

/**
 * Get notification badge variant for UI
 */
export const getNotificationBadgeVariant = (type: "info" | "success" | "warning" | "error") => {
  switch (type) {
    case "success":
      return "default";
    case "warning":
      return "secondary";
    case "error":
      return "destructive";
    default:
      return "outline";
  }
};
