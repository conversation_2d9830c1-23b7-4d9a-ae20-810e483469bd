import { useState, useEffect } from "react";
import moment from "moment";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export const useJobResponse = (jobId: string, status?: string) => {
  const [hasResponded, setHasResponded] = useState(false);
  const [responseCount, setResponseCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckingResponse, setIsCheckingResponse] = useState(false);

  const { toast } = useToast();

  useEffect(() => {
    const checkExistingResponse = async () => {
      try {
        setIsCheckingResponse(true);
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          setIsLoading(false);
          return;
        }

        const [responseResult, countResult] = await Promise.all([
          supabase
            .from("job_responses")
            .select("id")
            .eq("job_id", jobId)
            .eq("vakman_id", user.id)
            .maybeSingle(),
          supabase
            .from("job_responses")
            .select("id", { count: "exact" })
            .eq("job_id", jobId),
        ]);

        if (responseResult.error) throw responseResult.error;
        if (countResult.error) throw countResult.error;

        setResponseCount(countResult.count || 0);
        setHasResponded(!!responseResult.data);
      } catch (error) {
        console.error("Error checking response:", error);
        toast({
          variant: "destructive",
          title: "Fout bij controleren reacties",
          description:
            "Er is een probleem opgetreden bij het controleren van bestaande reacties.",
        });
      } finally {
        setIsLoading(false);
        setIsCheckingResponse(false);
      }
    };

    checkExistingResponse();
  }, [jobId, status, toast]);

  const handleJobResponse = async (message: string) => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error("No authenticated user");

      const {
        data: { created_at },
      } = await supabase
        .from("jobs")
        .select("created_at")
        .eq("id", jobId)
        .single();

      const { error } = await supabase.from("job_responses").insert({
        job_id: jobId,
        vakman_id: user.id,
        message: message || "Ik heb interesse in deze klus.",
        response_time_minutes: moment().diff(moment(created_at), "minutes"),
      });

      if (error) throw error;

      setHasResponded(true);

      // Create in-app notification for job owner
      try {
        // Get job details and owner info
        const { data: jobData, error: jobError } = await supabase
          .from("jobs")
          .select(
            `
            title,
            user_id,
            profiles:user_id (
              email,
              first_name,
              last_name
            )
          `
          )
          .eq("id", jobId)
          .single();

        // Get responder info
        const { data: responderData, error: responderError } = await supabase
          .from("profiles")
          .select("first_name, last_name, company_name")
          .eq("id", user.id)
          .single();

        if (!jobError && !responderError && jobData && responderData) {
          const responderName =
            responderData.company_name ||
            `${responderData.first_name} ${responderData.last_name}`.trim() ||
            "Een vakman";

          // Create notification for job owner
          const { error: notificationError } = await supabase.rpc(
            "create_notification",
            {
              p_user_id: jobData.user_id,
              p_title: "Nieuwe reactie ontvangen",
              p_message: `${responderName} heeft gereageerd op je klus '${jobData.title}'`,
              p_type: "success",
              p_action_url: "/gesprekken",
              p_metadata: {
                job_id: jobId,
                responder_id: user.id,
                responder_name: responderName,
              },
            }
          );

          if (notificationError) {
            console.error(
              "Error creating job response notification:",
              notificationError
            );
          }
        }
      } catch (error) {
        console.error("Error creating job response notification:", error);
        // Don't throw - this is non-critical
      }

      // We don't need to check response_time_minutes anymore as the bonus is handled by the database trigger
      toast({
        title: "Succesvol gereageerd",
        description:
          "Je reactie is succesvol verstuurd. Als je snel hebt gereageerd, ontvang je automatisch extra credits!",
      });
    } catch (error: any) {
      console.error("Error responding to job:", error);
      toast({
        variant: "destructive",
        title: "Fout bij reageren",
        description:
          error.message ||
          "Er is een probleem opgetreden bij het reageren op de klus.",
      });
      throw error;
    }
  };

  return {
    hasResponded,
    responseCount,
    isLoading,
    isCheckingResponse,
    handleJobResponse,
  };
};
